//
//  SynchronizedDisposeType.swift
//  RxSwift
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 10/25/15.
//  Copyright © 2015 <PERSON><PERSON><PERSON><PERSON>. All rights reserved.
//

protocol SynchronizedDisposeType: AnyObject, Disposable, Lock {
    func synchronized_dispose()
}

extension SynchronizedDisposeType {
    func synchronizedDispose() {
        self.lock(); defer { self.unlock() }
        self.synchronized_dispose()
    }
}
