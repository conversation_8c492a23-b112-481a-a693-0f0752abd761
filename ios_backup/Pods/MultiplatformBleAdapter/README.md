# MultiPlatformBleAdapter

Middle layer between crossplatform BLE libraries ([react-native-ble-plx](https://github.com/dotintent/react-native-ble-plx)
and [FlutterBleLib](https://github.com/dotintent/FlutterBleLib)) and iOS/Android system for manipulating Bluetooth Low Energy peripherals.
Internally uses:

- [RxAndroidBle](https://github.com/Polidea/RxAndroidBle)
- [RxBluetoothKit](https://github.com/Polidea/RxBluetoothKit)

If you'd like to use it in your own library, you should start from BleAdapter on either platform.

## Maintained by

This library is maintained by [Intent](https://withintent.com)

[Contact us](https://withintent.com/contact/)

## License

Copyright 2023 intent sp. z o.o

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
