{"name": "React", "version": "0.79.5", "summary": "A framework for building native apps using React", "description": "React Native apps are built using the React JS\nframework, and render directly to native UIKit\nelements using a fully asynchronous architecture.\nThere is no browser and no HTML. We have picked what\nwe think is the best set of features from these and\nother technologies to build what we hope to become\nthe best product development framework available,\nwith an emphasis on iteration speed, developer\ndelight, continuity of technology, and absolutely\nbeautiful and fast products with no compromises in\nquality or capability.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "preserve_paths": ["package.json", "LICENSE", "LICENSE-docs"], "cocoapods_version": ">= 1.10.1", "dependencies": {"React-Core": ["0.79.5"], "React-Core/DevSupport": ["0.79.5"], "React-Core/RCTWebSocket": ["0.79.5"], "React-RCTActionSheet": ["0.79.5"], "React-RCTAnimation": ["0.79.5"], "React-RCTBlob": ["0.79.5"], "React-RCTImage": ["0.79.5"], "React-RCTLinking": ["0.79.5"], "React-RCTNetwork": ["0.79.5"], "React-RCTSettings": ["0.79.5"], "React-RCTText": ["0.79.5"], "React-RCTVibration": ["0.79.5"]}}