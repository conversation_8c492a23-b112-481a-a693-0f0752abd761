{"name": "ReactCommon", "module_name": "ReactCommon", "version": "0.79.5", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "header_dir": "ReactCommon", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fast_float/include\" \"$(PODS_ROOT)/fmt/include\" \"$(PODS_ROOT)/Headers/Private/React-Core\"", "USE_HEADERMAP": "YES", "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "subspecs": [{"name": "turbomodule", "dependencies": {"React-callinvoker": ["0.79.5"], "React-perflogger": ["0.79.5"], "React-cxxreact": ["0.79.5"], "React-jsi": ["0.79.5"], "RCT-Folly": ["2024.11.18.00"], "React-logger": ["0.79.5"], "DoubleConversion": [], "fast_float": ["6.1.4"], "fmt": ["11.0.2"], "glog": [], "hermes-engine": []}, "subspecs": [{"name": "bridging", "dependencies": {"React-jsi": ["0.79.5"], "hermes-engine": []}, "source_files": "react/bridging/**/*.{cpp,h}", "exclude_files": "react/bridging/tests", "header_dir": "react/bridging", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_ROOT)/RCT-Folly\""}}, {"name": "core", "source_files": "react/nativemodule/core/ReactCommon/**/*.{cpp,h}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_debug.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_featureflags.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-utils/React_utils.framework/Headers\""}, "dependencies": {"React-debug": ["0.79.5"], "React-featureflags": ["0.79.5"], "React-utils": ["0.79.5"]}}]}]}