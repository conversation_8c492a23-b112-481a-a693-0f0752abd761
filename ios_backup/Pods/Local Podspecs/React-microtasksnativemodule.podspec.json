{"name": "React-microtasksnativemodule", "version": "0.79.5", "summary": "React Native microtasks native module", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "*.{cpp,h}", "header_dir": "react/nativemodule/microtasks", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CFLAGS": "$(inherited) -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/RCT-Folly\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\"", "DEFINES_MODULE": "YES"}, "dependencies": {"RCT-Folly": [], "React-jsi": [], "React-jsiexecutor": [], "hermes-engine": [], "React-hermes": [], "ReactCommon/turbomodule/core": [], "React-RCTFBReactNativeSpec": []}}