{"name": "react-native-ble-plx", "version": "3.5.0", "summary": "React Native Bluetooth Low Energy library", "homepage": "https://github.com/dotintent/react-native-ble-plx#readme", "license": "MIT", "authors": "dotintent (https://withintent.com/)", "platforms": {"ios": "11.0"}, "source": {"git": "https://github.com/dotintent/react-native-ble-plx.git", "tag": "3.5.0"}, "source_files": "ios/**/*.{h,m,mm}", "dependencies": {"MultiplatformBleAdapter": ["0.2.0"], "React-Core": [], "RCT-Folly": ["2024.11.18.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": []}, "compiler_flags": "-DMULTIPLATFORM_BLE_ADAPTER -fmodules -fcxx-modules  -DRCT_NEW_ARCH_ENABLED=1 -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -<PERSON><PERSON><PERSON><PERSON>Y_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}}