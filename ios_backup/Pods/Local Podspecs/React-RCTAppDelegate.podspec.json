{"name": "React-RCTAppDelegate", "version": "0.79.5", "summary": "An utility library to simplify common operations for the New Architecture", "homepage": "https://reactnative.dev/", "documentation_url": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.79.5"}, "source_files": "**/*.{c,h,m,mm,S,cpp}", "compiler_flags": "$(inherited) -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1 -DUSE_HERMES=1", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "$(PODS_TARGET_SRCROOT)/../../ReactCommon $(PODS_ROOT)/Headers/Private/React-Core $(PODS_ROOT)/boost $(PODS_ROOT)/DoubleConversion $(PODS_ROOT)/fast_float/include $(PODS_ROOT)/fmt/include $(PODS_ROOT)/RCT-Folly ${PODS_ROOT}/Headers/Public/FlipperKit $(PODS_ROOT)/Headers/Public/ReactCommon $(PODS_ROOT)/Headers/Public/React-RCTFabric $(PODS_ROOT)/Headers/Private/Yoga $(PODS_ROOT)/Headers/Public/React-hermes $(PODS_ROOT)/Headers/Public/hermes-engine \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime/RCTRuntime.framework/Headers\"", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1 -DUSE_HERMES=1", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES"}, "user_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/React-Core\" \"$(PODS_ROOT)/Headers/Private/Yoga\""}, "dependencies": {"React-Core": [], "RCT-Folly": ["2024.11.18.00"], "RCTRequired": [], "RCTTypeSafety": [], "React-RCTNetwork": [], "React-RCTImage": [], "React-CoreModules": [], "React-RCTFBReactNativeSpec": [], "React-defaultsnativemodule": [], "ReactCommon": [], "React-NativeModulesApple": [], "React-runtimescheduler": [], "React-RCTFabric": [], "React-RuntimeCore": [], "React-RuntimeApple": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-debug": [], "React-rendererdebug": [], "React-featureflags": [], "React-jsitooling": [], "React-RCTRuntime": [], "hermes-engine": [], "React-hermes": []}}